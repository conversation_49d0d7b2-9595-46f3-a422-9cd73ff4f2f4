// ==UserScript==
// @name         豆包与即梦 AI作图高清无水印下载
// @namespace    http://tampermonkey.net/
// @version      3.5
// @description  【v3.5 原图高质量下载】全新智能下载系统：并发测试多个高质量URL变体，自动选择最高分辨率版本；新增图片质量检测算法，确保下载真正的原图无水印版本。
// <AUTHOR> & You
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @icon         https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/web/logo/favicon.ico
// @grant        GM_addStyle
// @grant        GM_download
// @grant        GM_xmlhttpRequest
// @connect      p[0-9]-vdp.douyinpic.com
// @connect      p[0-9]-aio.ecombdimg.com
// @connect      sf-vdp.douyincdn.com
// @connect      jimeng.jianying.com
// @connect      www.doubao.com
// @connect      *
// ==/UserScript==

(function() {
    'use strict';

    // --- NEW: API Response Interceptor ---
    const interceptedResponses = new Map();
    const originalFetch = window.fetch;

    window.fetch = async function(...args) {
        const requestUrl = typeof args[0] === 'string' ? args[0] : args[0].url;
        // 先调用原始的fetch，让请求正常进行
        const response = await originalFetch.apply(this, args);

        const isApiUrl = requestUrl.includes('/api/v3/message/') ||
                         requestUrl.includes('/api/v3/content/post/detail/') ||
                         requestUrl.includes('/v2/api/gallery/post/detail');

        // 如果是我们需要的目标API，并且请求成功
        if (isApiUrl && response.ok) {
            console.log(`[AI Downloader] Intercepted successful API response from: ${requestUrl}`);
            // 克隆响应体，因为响应体只能被读取一次
            const clonedResponse = response.clone();
            clonedResponse.json().then(data => {
                if(data) {
                    // 将API的URL作为键，响应的JSON数据作为值，存入我们的缓存中
                    interceptedResponses.set(requestUrl, data);
                    console.log(`[AI Downloader] Cached data for ${requestUrl}`, data);
                }
            }).catch(e => {
                // 如果响应不是JSON格式，这里会报错，可以安全地忽略
            });
        }

        // 返回原始的响应，确保网站功能不受任何影响
        return response;
    };

    // --- 1. 全局设置 - 全新按钮和图标 ---
    const downloadIconSvg = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 17V5M8 13l4 4 4-4M4 21h16" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>`;
    const spinnerSvg = `<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke="white"><style>.spinner_V8m1{transform-origin:center;animation:spinner_zKoa 1.2s linear infinite}@keyframes spinner_zKoa{100%{transform:rotate(360deg)}}</style><g class="spinner_V8m1"><circle cx="12" cy="12" r="9.5" fill="none" stroke-width="3"></circle></g></svg>`;
    const successIconSvg = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 13l4 4L19 7" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>`;
    const errorIconSvg = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 6L6 18M6 6l12 12" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>`;

    const downloadButton = document.createElement('button');
    downloadButton.id = 'ai-image-download-button';
    document.body.appendChild(downloadButton);

    let currentImage = null; // Track the currently hovered image
    let hideButtonTimeout;

    // Draggable Button State
    let isDragging = false;
    let hasDragged = false;
    let startX, startY, offsetX, offsetY;

    // --- 2. 样式注入 - 全新样式和动画 ---
    GM_addStyle(`
        #ai-image-download-button {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: rgba(50, 50, 50, 0.35);
            border: 0.5px solid rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            cursor: grab;
            z-index: 2147483647;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);

            opacity: 0;
            transform: scale(0.95) translateZ(0);
            pointer-events: none;
            
            transition: opacity 0.1s cubic-bezier(0.4, 0, 0.2, 1), transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        #ai-image-download-button.visible {
            opacity: 1;
            transform: scale(1) translateZ(0);
            pointer-events: auto;
        }

        #ai-image-download-button:hover:not(.dragging) {
            background-color: rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.25);
        }
        #ai-image-download-button.dragging {
            cursor: grabbing;
            background-color: rgba(0, 0, 0, 0.2);
        }
    `);

    // --- 3. 核心功能函数 ---

    /**
     * 从 document.cookie 中读取指定的 cookie 值
     * @param {string} name - cookie的名称
     * @returns {string|undefined} - cookie的值
     */
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }
    
    /**
     * 统一的API请求函数
     * @param {string} url - API端点
     * @returns {Promise<object>} - 解析后的JSON响应
     */
    async function apiFetch(url) {
        console.log(`[AI Downloader] API Request (using fetch with enhanced headers): ${url}`);
        try {
            // 获取多种可能的认证令牌
            const csrfToken = getCookie('csrftoken') || getCookie('csrf_token') || getCookie('_csrf');

            const headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'X-Requested-With': 'XMLHttpRequest',
                'User-Agent': navigator.userAgent,
                'Referer': window.location.href,
                'Origin': window.location.origin
            };

            // 添加找到的认证令牌
            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken;
                headers['X-CSRF-TOKEN'] = csrfToken;
                console.log('[AI Downloader] Found CSRF Token, adding to headers.');
            }

            // 使用 window.fetch 来确保自动携带浏览器登录的cookies
            const response = await window.fetch(url, {
                method: 'GET',
                headers,
                credentials: 'include', // 确保包含cookies
                mode: 'cors'
            });

            if (!response.ok) {
                throw new Error(`HTTP Error ${response.status}`);
            }

            const data = await response.json();
            if (!data) {
                 throw new Error(`API Error: Response is empty or not valid JSON.`);
            }

            console.log(`[AI Downloader] API Success for: ${url}`, data);
            return data;

        } catch (error) {
            console.error(`[AI Downloader] Fetch for ${url} failed:`, error);
            throw error;
        }
    }

    /**
     * 针对"即梦"网站特殊优化的最佳图片URL获取函数 (API-only)
     * @param {HTMLImageElement} img - 目标图片元素
     * @returns {Promise<string|null>}
     */
    async function getJimengBestUrl(img) {
        let postId = null;
        try {
            const link = img.closest('a');
            if (link && link.href) {
                const match = link.href.match(/\/post\/(\d+)/);
                if (match && match[1]) postId = match[1];
            }
            if (!postId) {
                const container = img.closest('[data-id]');
                postId = container?.dataset?.id;
            }

            if (!postId) throw new Error("无法找到 Post ID");

            const apiUrl = `https://jimeng.jianying.com/v2/api/gallery/post/detail?post_id=${postId}`;
            
            // 优先从拦截的缓存中查找数据
            if (interceptedResponses.has(apiUrl)) {
                console.log('[AI Downloader] Found cached API response for Jimeng:', apiUrl);
                const data = interceptedResponses.get(apiUrl);
                const imageInfo = data?.data?.post_info?.image_list?.[0];
                const highResUrl = imageInfo?.url_origin || imageInfo?.raw_url || imageInfo?.url_hd;
                if (highResUrl) return highResUrl;
            }

            // 如果缓存中没有，再尝试API请求作为后备
            console.log('[AI Downloader] No cached response, trying direct fetch for Jimeng:', apiUrl);
            const data = await apiFetch(apiUrl);
            const imageInfo = data?.data?.post_info?.image_list?.[0];
            const highResUrl = imageInfo?.url_origin || imageInfo?.raw_url || imageInfo?.url_hd;

            if (!highResUrl) throw new Error("API响应中未找到高清图片URL");
            return highResUrl;

        } catch (error) {
            console.error("即梦 API 流程失败:", error);
            // API失败后，回退到清理当前图片src的URL
            return cleanUrl(img.src, 'jimeng');
        }
    }

    /**
     * 针对"豆包"网站特殊优化的最佳图片URL获取函数 (API-only)
     * @param {HTMLImageElement} img - 目标图片元素
     * @returns {Promise<string|null>}
     */
    async function getDoubaoBestUrl(img) {
        // 首先尝试从图片的原始src中提取更高质量的URL
        const originalSrc = img.src;
        console.log(`[AI Downloader] 开始处理豆包图片: ${originalSrc}`);

        // 尝试多种API端点和ID提取方式
        const apiAttempts = [];

        // 尝试对话页
        const messageContainer = img.closest('[data-message-id]');
        if (messageContainer?.dataset?.messageId) {
            const messageId = messageContainer.dataset.messageId;
            apiAttempts.push({
                url: `https://www.doubao.com/api/v3/message/${messageId}`,
                source: '豆包对话页',
                extractor: (data) => data?.data?.message?.[0]?.content?.[0]?.image?.url_origin
            });
        }

        // 尝试广场页
        const postContainer = img.closest('[data-content-id]');
        if (postContainer?.dataset?.contentId) {
            const contentId = postContainer.dataset.contentId;
            apiAttempts.push({
                url: `https://www.doubao.com/api/v3/content/post/detail/${contentId}`,
                source: '豆包广场页',
                extractor: (data) => data?.data?.post?.images?.[0]?.url_origin
            });
        }

        // 尝试从图片URL中提取ID进行API调用
        const urlMatch = originalSrc.match(/\/([a-f0-9]{32}|[0-9]{19})/);
        if (urlMatch && urlMatch[1]) {
            const extractedId = urlMatch[1];
            apiAttempts.push({
                url: `https://www.doubao.com/api/v3/image/${extractedId}`,
                source: '豆包图片ID',
                extractor: (data) => data?.data?.url_origin || data?.data?.original_url
            });
        }

        // 依次尝试所有API端点
        for (const attempt of apiAttempts) {
            // 优先从拦截的缓存中查找数据
            if (interceptedResponses.has(attempt.url)) {
                console.log(`[AI Downloader] Found cached API response for ${attempt.source}:`, attempt.url);
                const data = interceptedResponses.get(attempt.url);
                const imageUrl = attempt.extractor(data);
                if (imageUrl) return imageUrl;
            }

            // 如果缓存没有，尝试直接调用
            try {
                console.log(`[AI Downloader] No cached response, trying direct fetch for ${attempt.source}:`, attempt.url);
                const data = await apiFetch(attempt.url);
                const imageUrl = attempt.extractor(data);
                if (imageUrl) return imageUrl;
            } catch(e) {
                console.error(`${attempt.source} API direct fetch failed:`, e.message);
                if (e.message.includes('401')) {
                    console.warn('[AI Downloader] 401认证错误，可能需要刷新页面或重新登录豆包');
                }
                // 继续尝试下一个API端点
                continue;
            }
        }

        console.warn("豆包 所有API尝试失败，回退到智能URL清理。");

        // 尝试智能URL重构，构造可能的高质量版本
        const highQualityUrl = tryConstructHighQualityUrl(originalSrc);
        if (highQualityUrl && highQualityUrl !== originalSrc) {
            console.log(`[AI Downloader] 尝试构造的高质量URL: ${highQualityUrl}`);
            return highQualityUrl;
        }

        // 最后回退到URL清理
        return cleanUrl(originalSrc, 'doubao');
    }

    /**
     * 尝试从现有URL构造高质量版本
     * @param {string} url - 原始URL
     * @returns {string|null} - 构造的高质量URL或null
     */
    function tryConstructHighQualityUrl(url) {
        try {
            const urlObj = new URL(url);

            // 豆包图片URL模式分析和重构
            if (urlObj.hostname.includes('byteimg.com') || urlObj.hostname.includes('douyincdn.com')) {
                let pathname = urlObj.pathname;

                // 提取基础文件名（移除所有处理参数）
                const baseNameMatch = pathname.match(/\/([^\/]+?)(?:~[^\/]*)?\.([a-z]+)$/i);
                if (baseNameMatch) {
                    const [, , extension] = baseNameMatch;

                    // 构造可能的高质量URL变体
                    const qualityVariants = [
                        // 原始质量版本
                        `${pathname.replace(/~[^\/]*\.[^\/]*$/, '')}.${extension}`,
                        // 高清版本
                        `${pathname.replace(/~[^\/]*\.[^\/]*$/, '')}~tplv-original.${extension}`,
                        // 无水印版本
                        `${pathname.replace(/~[^\/]*\.[^\/]*$/, '')}~noop.${extension}`,
                        // 直接使用基础名称
                        pathname.replace(/~[^\/]*(\.[^\/]*)$/, '$1')
                    ];

                    // 返回第一个看起来最干净的版本
                    for (const variant of qualityVariants) {
                        if (!variant.includes('watermark') && !variant.includes('thumb')) {
                            urlObj.pathname = variant;
                            urlObj.search = ''; // 清除所有查询参数
                            return urlObj.href;
                        }
                    }
                }

                // 如果上述方法失败，尝试域名转换
                if (urlObj.hostname.includes('ecombdimg.com')) {
                    urlObj.hostname = 'sf-vdp.douyincdn.com';
                    urlObj.pathname = urlObj.pathname.replace(/\/tos-cn-i-[a-z0-9]+\//, '/obj/');
                    urlObj.search = '';
                    return urlObj.href;
                }
            }

            return null;
        } catch (e) {
            console.error('[AI Downloader] Error constructing high quality URL:', e);
            return null;
        }
    }

    /**
     * 清理URL，移除水印和缩略图参数
     * @param {string} url - 原始URL
     * @param {string} [site] - 可选, 'doubao' 或 'jimeng'
     * @returns {string|null}
     */
    function cleanUrl(url, site) {
        if (!url || !url.startsWith('http')) return null;
        try {
            const urlObj = new URL(url);

            // 完全清除所有查询参数，因为豆包的水印通常通过查询参数控制
            urlObj.search = '';

            // 豆包特殊处理：彻底清理文件名中的水印标识
            if (site === 'doubao') {
                let pathname = urlObj.pathname;

                // 移除文件名中的水印和缩略图标识
                // 匹配模式：~tplv-xxx-xxx-watermark-v2.jpeg 等
                pathname = pathname.replace(/~tplv-[^~]+-(watermark|thumb|web-thumb-watermark)[^.]*\.(jpeg|jpg|png|webp)/gi, '.jpeg');

                // 移除其他常见的水印标识
                pathname = pathname.replace(/_thumb|_preview|_small|@\d+w|_\d+x\d+|~\d+x\d+/g, '');
                pathname = pathname.replace(/~[^~]*watermark[^.]*/gi, '');
                pathname = pathname.replace(/~[^~]*thumb[^.]*/gi, '');
                pathname = pathname.replace(/~[^~]*resize[^.]*/gi, '');
                pathname = pathname.replace(/~[^~]*quality[^.]*/gi, '');

                // 如果文件名被完全清理掉了扩展名，添加默认扩展名
                if (!pathname.match(/\.(jpeg|jpg|png|webp)$/i)) {
                    pathname += '.jpeg';
                }

                urlObj.pathname = pathname;

                // 域名转换优化
                if (urlObj.hostname.includes('ecombdimg.com')) {
                    urlObj.hostname = 'sf-vdp.douyincdn.com';
                    urlObj.pathname = urlObj.pathname.replace(/\/tos-cn-i-[a-z0-9]+\//, '/obj/');
                }

                // 尝试转换为更高质量的域名
                if (urlObj.hostname.includes('byteimg.com')) {
                    // 保持原域名，但确保路径干净
                    urlObj.pathname = urlObj.pathname.replace(/\/resize\/[^\/]+/g, '');
                    urlObj.pathname = urlObj.pathname.replace(/\/thumbnail\/[^\/]+/g, '');
                    urlObj.pathname = urlObj.pathname.replace(/\/watermark\/[^\/]+/g, '');
                    urlObj.pathname = urlObj.pathname.replace(/\/quality\/[^\/]+/g, '');
                    urlObj.pathname = urlObj.pathname.replace(/\/format\/[^\/]+/g, '');
                }

                // 如果路径包含image处理标识，尝试获取原始路径
                if (urlObj.pathname.includes('/image/')) {
                    const parts = urlObj.pathname.split('/image/');
                    if (parts.length > 1) {
                        urlObj.pathname = '/' + parts[1];
                    }
                }
            } else {
                // 非豆包网站的清理逻辑
                const paramsToRemove = [
                    'watermark', 'wmk', 'w', 'h', 's', 'size', 'quality', 'format',
                    'interlace', 'thumbnail', 'crop', 'resize', 'jpeg_quality',
                    'x-oss-process', 'imageMogr2', 'imageView2', 'imageslim',
                    'mark', 'logo', 'text', 'color', 'font', 'opacity',
                    'position', 'voffset', 'hoffset', 'blur', 'bright',
                    'contrast', 'sharpen', 'auto-orient', 'strip', 'progressive',
                    'q', 'f', 'rotate'
                ];
                paramsToRemove.forEach(p => urlObj.searchParams.delete(p));
                urlObj.pathname = urlObj.pathname.replace(/_thumb|_preview|_small|@\d+w|_\d+x\d+|~\d+x\d+/g, '');
            }

            const cleanedUrl = urlObj.href;
            console.log(`[AI Downloader] Cleaned ${site} URL: ${url} -> ${cleanedUrl}`);
            return cleanedUrl;

        } catch (e) {
            console.error('[AI Downloader] Error cleaning URL:', e);
            return url;
        }
    }

    /**
     * 清理文本，使其成为合法、简短的文件名
     * @param {string} text - 原始文本
     * @returns {string|null} - 清理后的文件名
     */
    function sanitizeFilename(text) {
        if (!text) return null;
        // 优先使用中文逗号、句号或英文逗号、句点作为分隔，取第一部分
        let shortText = text.split(/[,.，。]/)[0];
        // 如果没有分隔符，则截取前10个词或80个字符
        if (shortText.length === text.length) {
            const words = shortText.split(/\s+/);
            if(words.length > 10) {
                 shortText = words.slice(0, 10).join(' ');
            } else if (shortText.length > 80) {
                 shortText = shortText.substring(0, 80);
            }
        }
        return shortText.replace(/[\\/:*?"<>|]/g, ' ').replace(/\s+/g, ' ').trim() || null;
    }

    /**
     * 寻找并返回与图片关联的提示词(prompt)
     * @param {HTMLImageElement} img - 目标图片元素
     * @returns {string|null} - 找到的提示词文本
     */
    function findPromptText(img) {
        let container, promptEl;
        if(window.location.hostname.includes('doubao')) {
             const messageContainer = img.closest('.chat-message');
             if (messageContainer) {
                 const sibling = messageContainer.previousElementSibling;
                 if(sibling?.classList.contains('chat-message')) {
                     promptEl = sibling.querySelector('.markdown-body p');
                     if(promptEl) return promptEl.textContent;
                 }
             }
        } else { // jimeng
            const detailContainer = document.querySelector('.detail-right-container, .main-right');
            if (detailContainer) {
                promptEl = detailContainer.querySelector('.prompt-text, .description-text, .text-content');
                if (promptEl?.textContent) return promptEl.textContent;
            }
            container = img.closest('.image-card, .image-wall-item');
            if (container) {
               promptEl = container.querySelector('.prompt, .desc, .text-ellipsis-2, p');
            }
        }

        if (promptEl && promptEl.textContent) return promptEl.textContent;
        return img.alt || null;
    }

    /**
     * 获取高质量URL候选列表
     * @param {HTMLImageElement} img - 目标图片元素
     * @returns {Promise<string[]>} - URL候选列表，按优先级排序
     */
    async function getHighQualityUrlCandidates(img) {
        const candidates = [];
        const originalSrc = img.src;

        if (window.location.hostname.includes('jimeng.jianying.com')) {
            // 即梦网站处理
            const jimengUrl = await getJimengBestUrl(img);
            if (jimengUrl) candidates.push(jimengUrl);

            // 添加清理后的原始URL作为备选
            const cleanedUrl = cleanUrl(originalSrc, 'jimeng');
            if (cleanedUrl && cleanedUrl !== jimengUrl) candidates.push(cleanedUrl);

        } else {
            // 豆包网站处理
            const doubaoUrl = await getDoubaoBestUrl(img);
            if (doubaoUrl) candidates.push(doubaoUrl);

            // 添加多种高质量变体
            const qualityVariants = generateDoubaoQualityVariants(originalSrc);
            candidates.push(...qualityVariants);

            // 添加清理后的原始URL作为最后备选
            const cleanedUrl = cleanUrl(originalSrc, 'doubao');
            if (cleanedUrl && !candidates.includes(cleanedUrl)) {
                candidates.push(cleanedUrl);
            }
        }

        // 去重并过滤无效URL
        const uniqueCandidates = [...new Set(candidates)].filter(url =>
            url && url.startsWith('http') && !url.includes('undefined')
        );

        console.log(`[AI Downloader] 生成了 ${uniqueCandidates.length} 个候选URL:`, uniqueCandidates);
        return uniqueCandidates;
    }

    /**
     * 生成豆包图片的多种高质量变体URL
     * @param {string} originalUrl - 原始URL
     * @returns {string[]} - 高质量变体URL列表
     */
    function generateDoubaoQualityVariants(originalUrl) {
        const variants = [];

        try {
            const urlObj = new URL(originalUrl);
            const pathname = urlObj.pathname;

            // 提取基础路径和文件信息
            const pathMatch = pathname.match(/^(.+\/)([^\/]+)(\.[^.]+)$/);
            if (!pathMatch) return variants;

            const [, basePath, filename, extension] = pathMatch;

            // 移除现有的处理参数，生成原始文件名
            const cleanFilename = filename.replace(/~[^~]*$/, '');

            // 生成多种高质量变体
            const qualityParams = [
                '', // 无参数版本（最原始）
                '~noop', // 无操作版本
                '~tplv-original', // 原始版本
                '~tplv-obj', // 对象存储版本
                '~tplv-large', // 大尺寸版本
                '~quality-100', // 最高质量版本
            ];

            qualityParams.forEach(param => {
                const newPathname = basePath + cleanFilename + param + extension;
                const variantUrl = new URL(originalUrl);
                variantUrl.pathname = newPathname;
                variantUrl.search = ''; // 清除所有查询参数
                variants.push(variantUrl.href);
            });

            // 尝试域名转换版本
            if (urlObj.hostname.includes('ecombdimg.com')) {
                const convertedUrl = new URL(originalUrl);
                convertedUrl.hostname = 'sf-vdp.douyincdn.com';
                convertedUrl.pathname = convertedUrl.pathname.replace(/\/tos-cn-i-[a-z0-9]+\//, '/obj/');
                convertedUrl.search = '';
                variants.push(convertedUrl.href);
            }

        } catch (e) {
            console.error('[AI Downloader] Error generating quality variants:', e);
        }

        return variants;
    }

    /**
     * 下载并选择最高质量的图片
     * @param {string[]} urlCandidates - URL候选列表
     * @returns {Promise<{url: string, blob: Blob, width: number, height: number}|null>}
     */
    async function downloadBestQualityImage(urlCandidates) {
        // 并发测试所有URL
        const downloadPromises = urlCandidates.map(async (url, index) => {
            try {
                console.log(`[AI Downloader] 测试URL ${index + 1}/${urlCandidates.length}: ${url}`);

                const response = await new Promise((resolve, reject) => {
                    GM_xmlhttpRequest({
                        method: "GET",
                        url: url,
                        responseType: 'blob',
                        headers: {
                            'Referer': window.location.href,
                            'User-Agent': navigator.userAgent
                        },
                        timeout: 10000, // 10秒超时
                        onload: resolve,
                        onerror: reject,
                        ontimeout: reject,
                    });
                });

                if (response.status !== 200) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const blob = response.response;
                if (!blob || blob.size === 0) {
                    throw new Error('空响应');
                }

                // 获取图片尺寸
                const dimensions = await getImageDimensions(blob);

                const result = {
                    url,
                    blob,
                    width: dimensions.width,
                    height: dimensions.height,
                    size: blob.size,
                    index
                };

                console.log(`[AI Downloader] URL ${index + 1} 成功: ${dimensions.width}x${dimensions.height}, ${(blob.size/1024).toFixed(1)}KB`);
                return result;

            } catch (error) {
                console.log(`[AI Downloader] URL ${index + 1} 失败: ${error.message}`);
                return null;
            }
        });

        // 等待所有下载完成
        const downloadResults = await Promise.all(downloadPromises);
        const validResults = downloadResults.filter(result => result !== null);

        if (validResults.length === 0) {
            return null;
        }

        // 选择最高质量的图片（优先级：尺寸 > 文件大小 > URL顺序）
        const bestResult = validResults.reduce((best, current) => {
            const bestPixels = best.width * best.height;
            const currentPixels = current.width * current.height;

            // 优先选择像素更多的
            if (currentPixels > bestPixels) return current;
            if (currentPixels < bestPixels) return best;

            // 像素相同时选择文件更大的
            if (current.size > best.size) return current;
            if (current.size < best.size) return best;

            // 都相同时选择索引更小的（优先级更高）
            return current.index < best.index ? current : best;
        });

        return bestResult;
    }

    /**
     * 获取图片的尺寸信息
     * @param {Blob} blob - 图片Blob
     * @returns {Promise<{width: number, height: number}>}
     */
    function getImageDimensions(blob) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const url = URL.createObjectURL(blob);

            img.onload = () => {
                URL.revokeObjectURL(url);
                resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight
                });
            };

            img.onerror = () => {
                URL.revokeObjectURL(url);
                reject(new Error('无法加载图片'));
            };

            img.src = url;
        });
    }

    /**
     * 生成最终的文件名
     * @param {HTMLImageElement} img - 目标图片元素
     * @param {string} url - 图片的URL
     * @returns {string} - 最终的文件名
     */
    function getFilename(img, url) {
        const promptText = findPromptText(img);
        const sanitizedPrompt = sanitizeFilename(promptText);
        if (sanitizedPrompt) return `${sanitizedPrompt}.png`;

        try {
            let filename = new URL(url).pathname.split('/').pop().replace(/\.[^/.]+$/, "");
            return filename ? `${filename}.png` : 'downloaded_image.png';
        } catch {
            return 'downloaded_image.png';
        }
    }

    /**
     * 将任何图片格式的Blob转换为PNG格式的Blob
     * @param {Blob} blob - 原始图片Blob
     * @returns {Promise<Blob>} - PNG格式的Blob
     */
    function convertToPng(blob) {
        return new Promise((resolve, reject) => {
            if (blob.type === 'image/png') {
                resolve(blob);
                return;
            }
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const url = URL.createObjectURL(blob);

            img.onload = () => {
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                ctx.drawImage(img, 0, 0);
                canvas.toBlob(resolve, 'image/png', 1.0); // 1.0 is for highest quality
                URL.revokeObjectURL(url);
            };
            img.onerror = (err) => {
                URL.revokeObjectURL(url);
                reject(err);
            };
            img.src = url;
        });
    }

    /**
     * 显示下载按钮
     */
    function showButton(targetImage) {
        clearTimeout(hideButtonTimeout);
        currentImage = targetImage; // Set the context for the click handler
        
        if (!downloadButton.disabled) {
            downloadButton.innerHTML = downloadIconSvg;
        }

        if (!isDragging) {
            const rect = targetImage.getBoundingClientRect();
            const btnSize = 30;
            // 定位在右下角
            downloadButton.style.top = `${window.scrollY + rect.bottom - btnSize - 10}px`;
            downloadButton.style.left = `${window.scrollX + rect.right - btnSize - 10}px`;
        }
        
        downloadButton.classList.add('visible');
    }

    /**
     * 隐藏下载按钮
     */
    function hideButton() {
        hideButtonTimeout = setTimeout(() => {
            downloadButton.classList.remove('visible');
        }, 200);
    }

    // --- 4. 事件处理 ---

    /**
     * 为有效图片元素附加事件监听器
     * @param {HTMLElement} element - 目标元素（通常是图片或其容器）
     */
    function attachListeners(element) {
        if (element.dataset.dlListenerAttached) return;
        element.dataset.dlListenerAttached = 'true';

        const img = element.tagName === 'IMG' ? element : element.querySelector('img');
        if (!img) return;

        const hoverTarget = img.closest('.image-card, .image-wall-item, .message-content-image-wrapper') || img;

        hoverTarget.addEventListener('mouseenter', () => showButton(img));
        hoverTarget.addEventListener('mouseleave', () => hideButton());
    }

    /**
     * 扫描并处理页面上所有符合条件的图片
     */
    function processPage() {
        document.querySelectorAll('img:not([data-dl-listener-attached])').forEach(img => {
            if (img.closest('[data-dl-listener-attached]')) return;
            const process = () => {
                // BUG FIX: Removed the restrictive check that only allowed the button on .chat-message
                if (img.naturalWidth < 150 || img.naturalHeight < 150) return;
                attachListeners(img);
            };
            if (img.complete) process();
            else img.onload = process;
        });
    }

    // --- NEW: Drag & Drop and Click Logic ---
    function onMouseMove(e) {
        if (!isDragging) return;
        if (!hasDragged && (Math.abs(e.clientX - startX) > 5 || Math.abs(e.clientY - startY) > 5)) {
            hasDragged = true;
            downloadButton.classList.add('dragging');
        }
        if (hasDragged) {
            e.preventDefault();
            downloadButton.style.left = `${e.pageX - offsetX}px`;
            downloadButton.style.top = `${e.pageY - offsetY}px`;
        }
    }

    function onMouseUp() {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        if (isDragging) {
            isDragging = false;
            downloadButton.classList.remove('dragging');
        }
    }

    downloadButton.addEventListener('mousedown', (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        hasDragged = false;
        startX = e.clientX;
        startY = e.clientY;
        offsetX = e.clientX - downloadButton.getBoundingClientRect().left;
        offsetY = e.clientY - downloadButton.getBoundingClientRect().top;
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    });

    downloadButton.addEventListener('click', async (e) => {
        if (hasDragged) { e.preventDefault(); e.stopPropagation(); return; }
        if (downloadButton.disabled || !currentImage) return;

        downloadButton.disabled = true;
        downloadButton.innerHTML = spinnerSvg;

        try {
            // 获取多个可能的高质量URL
            const urlCandidates = await getHighQualityUrlCandidates(currentImage);

            if (!urlCandidates || urlCandidates.length === 0) {
                throw new Error("无法获取任何有效的图片URL");
            }

            console.log(`[AI Downloader] 找到 ${urlCandidates.length} 个候选URL，开始质量检测`);

            // 尝试下载最高质量的版本
            const bestResult = await downloadBestQualityImage(urlCandidates);

            if (!bestResult) {
                throw new Error("所有URL都无法下载");
            }

            const filename = getFilename(currentImage, bestResult.url);
            console.log(`[AI Downloader] 最终选择URL: ${bestResult.url}, 图片尺寸: ${bestResult.width}x${bestResult.height}`);

            // 转换为PNG并下载
            const pngBlob = await convertToPng(bestResult.blob);
            const blobUrl = URL.createObjectURL(pngBlob);
            const a = document.createElement('a');
            a.href = blobUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(blobUrl);

            downloadButton.innerHTML = successIconSvg;

        } catch (error) {
            console.error("下载流程失败:", error);
            downloadButton.innerHTML = errorIconSvg;
        } finally {
            setTimeout(() => {
                downloadButton.disabled = false;
                if (downloadButton.classList.contains('visible')) {
                    downloadButton.innerHTML = downloadIconSvg;
                }
            }, 2000);
        }
    });

    // --- Hover Stability Fix ---
    downloadButton.addEventListener('mouseenter', () => clearTimeout(hideButtonTimeout) );
    downloadButton.addEventListener('mouseleave', () => hideButton() );

    // --- 5. 脚本执行入口 ---
    // 使用MutationObserver监控DOM变化，以支持无限滚动等动态加载内容的页面
    const observer = new MutationObserver(() => setTimeout(processPage, 500));
    observer.observe(document.body, { childList: true, subtree: true });

    // 初始执行
    window.addEventListener('load', () => {
        setTimeout(processPage, 1000);
    });

    console.log('豆包/即梦 高清无水印下载脚本已启动 (v3.2)');
})();
